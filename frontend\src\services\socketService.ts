import { io, Socket } from 'socket.io-client';

class SocketService {
  private socket: Socket | null = null;
  private token: string | null = null;

  connect(token: string) {
    this.token = token;
    
    this.socket = io('http://localhost:5000', {
      auth: {
        token: token
      }
    });

    this.socket.on('connect', () => {
      console.log('Connected to server');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket() {
    return this.socket;
  }

  isConnected() {
    return this.socket?.connected || false;
  }

  // Join a conversation room
  joinConversation(contactId: string) {
    if (this.socket) {
      this.socket.emit('join_conversation', contactId);
    }
  }

  // Leave a conversation room
  leaveConversation(contactId: string) {
    if (this.socket) {
      this.socket.emit('leave_conversation', contactId);
    }
  }

  // Send a message
  sendMessage(receiverId: string, content: string, messageType: string = 'text') {
    if (this.socket) {
      this.socket.emit('send_message', {
        receiverId,
        content,
        messageType
      });
    }
  }

  // Mark messages as read
  markMessagesAsRead(contactId: string) {
    if (this.socket) {
      this.socket.emit('mark_messages_read', contactId);
    }
  }

  // Typing indicators
  startTyping(contactId: string) {
    if (this.socket) {
      this.socket.emit('typing_start', contactId);
    }
  }

  stopTyping(contactId: string) {
    if (this.socket) {
      this.socket.emit('typing_stop', contactId);
    }
  }

  // Event listeners
  onNewMessage(callback: (message: any) => void) {
    if (this.socket) {
      this.socket.on('new_message', callback);
    }
  }

  onMessageNotification(callback: (notification: any) => void) {
    if (this.socket) {
      this.socket.on('message_notification', callback);
    }
  }

  onUserTyping(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('user_typing', callback);
    }
  }

  onUserStoppedTyping(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('user_stopped_typing', callback);
    }
  }

  onMessagesRead(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('messages_read', callback);
    }
  }

  // Remove event listeners
  offNewMessage() {
    if (this.socket) {
      this.socket.off('new_message');
    }
  }

  offMessageNotification() {
    if (this.socket) {
      this.socket.off('message_notification');
    }
  }

  offUserTyping() {
    if (this.socket) {
      this.socket.off('user_typing');
    }
  }

  offUserStoppedTyping() {
    if (this.socket) {
      this.socket.off('user_stopped_typing');
    }
  }

  offMessagesRead() {
    if (this.socket) {
      this.socket.off('messages_read');
    }
  }
}

// Create a singleton instance
const socketService = new SocketService();
export default socketService;
