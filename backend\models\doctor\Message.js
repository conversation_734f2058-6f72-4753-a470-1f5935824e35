const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  receiverId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  senderName: { // Denormalized for display
    type: String,
    required: true,
  },
  receiverName: { // Denormalized for display
    type: String,
    required: true,
  },
  content: {
    type: String,
    required: true,
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'file'],
    default: 'text',
  },
  isRead: {
    type: Boolean,
    default: false,
    index: true,
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
  // Conversation identifier for easier querying
  conversationId: {
    type: String,
    required: true,
    index: true,
  },
  // To ensure only doctor-patient communication based on appointments
  appointmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Appointment',
    required: true,
    index: true,
  },
});

// Create compound index for efficient conversation queries
messageSchema.index({ conversationId: 1, timestamp: 1 });
messageSchema.index({ senderId: 1, receiverId: 1 });

// Static method to generate conversation ID
messageSchema.statics.generateConversationId = function(userId1, userId2) {
  // Always put the smaller ID first to ensure consistent conversation IDs
  const ids = [userId1.toString(), userId2.toString()].sort();
  return `${ids[0]}_${ids[1]}`;
};

const Message = mongoose.model('Message', messageSchema);
module.exports = Message;