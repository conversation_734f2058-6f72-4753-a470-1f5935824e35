
import { useState, useEffect, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Send,
  PaperclipIcon,
  Clock,
  ImageIcon,
  FileIcon,
  UserRound,
  MessageSquare as MessageIcon
} from "lucide-react";
import { formatDistance } from "date-fns";
import { toast } from "@/hooks/use-toast";
import messageService, { MessageData, ConversationData, ContactData } from "@/services/messageService";
import socketService from "@/services/socketService";

// Interface for typing indicator
interface TypingUser {
  userId: string;
  userName: string;
}

const Messages = () => {
  const { user } = useAuth();
  const [searchParams] = useSearchParams();
  const initialContactId = searchParams.get("patientId") || searchParams.get("doctorId") || "";

  const [conversations, setConversations] = useState<ConversationData[]>([]);
  const [messages, setMessages] = useState<MessageData[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedContactId, setSelectedContactId] = useState(initialContactId);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Initialize Socket.IO connection and load conversations
  useEffect(() => {
    if (!user) return;

    const token = localStorage.getItem('token');
    if (!token) return;

    // Connect to Socket.IO
    const socket = socketService.connect(token);
    setIsConnected(socketService.isConnected());

    // Load conversations
    loadConversations();

    // Set up Socket.IO event listeners
    socketService.onNewMessage((message: MessageData) => {
      setMessages(prev => [...prev, message]);
    });

    socketService.onMessageNotification((notification) => {
      // Update conversation list with new message
      loadConversations();

      // Show toast notification if not in the conversation
      if (notification.senderId !== selectedContactId) {
        toast({
          title: "New message",
          description: `${notification.senderName}: ${notification.message.content.substring(0, 50)}...`,
        });
      }
    });

    socketService.onUserTyping((data) => {
      setTypingUsers(prev => {
        const existing = prev.find(u => u.userId === data.userId);
        if (!existing) {
          return [...prev, { userId: data.userId, userName: data.userName }];
        }
        return prev;
      });
    });

    socketService.onUserStoppedTyping((data) => {
      setTypingUsers(prev => prev.filter(u => u.userId !== data.userId));
    });

    socketService.onMessagesRead((data) => {
      // Update message read status
      setMessages(prev =>
        prev.map(msg =>
          msg.conversationId === data.conversationId && msg.senderId._id === user.id
            ? { ...msg, isRead: true }
            : msg
        )
      );
    });

    if (initialContactId) {
      setSelectedContactId(initialContactId);
    }

    return () => {
      socketService.disconnect();
      setIsConnected(false);
    };
  }, [user, initialContactId]);

  // Load conversations from API
  const loadConversations = async () => {
    try {
      setIsLoading(true);
      const conversationsData = await messageService.getConversations();
      setConversations(conversationsData);
    } catch (error) {
      console.error('Error loading conversations:', error);
      toast({
        title: "Error",
        description: "Failed to load conversations",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Filter conversations based on search term
  const filteredConversations = conversations.filter(conv =>
    conv.contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.contact.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get selected contact from conversations
  const selectedConversation = conversations.find(conv => conv.contactId === selectedContactId);
  const selectedContact = selectedConversation?.contact;

  // Load messages for selected contact
  useEffect(() => {
    if (selectedContactId && user) {
      loadMessages(selectedContactId);

      // Join conversation room
      socketService.joinConversation(selectedContactId);

      // Mark messages as read
      socketService.markMessagesAsRead(selectedContactId);

      return () => {
        // Leave conversation room when switching contacts
        socketService.leaveConversation(selectedContactId);
      };
    }
  }, [selectedContactId, user]);

  // Load messages for a specific contact
  const loadMessages = async (contactId: string) => {
    try {
      setIsLoading(true);
      const messagesData = await messageService.getMessages(contactId);
      setMessages(messagesData);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast({
        title: "Error",
        description: "Failed to load messages",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Scroll to bottom of messages when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);



  // Get initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  // Format time for message display
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ' ' +
             date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  // Get time since for contact list
  const getTimeSince = (timestamp: string) => {
    try {
      return formatDistance(new Date(timestamp), new Date(), { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };



  // Send a new message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedContactId || !user) return;

    try {
      // Send via Socket.IO for real-time delivery
      socketService.sendMessage(selectedContactId, newMessage.trim());

      // Also send via API for persistence
      await messageService.sendMessage(selectedContactId, newMessage.trim());

      setNewMessage("");

      // Stop typing indicator
      if (isTyping) {
        socketService.stopTyping(selectedContactId);
        setIsTyping(false);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  };

  // Handle file attachment (mock function)
  const handleAttachment = () => {
    toast({
      title: "Feature coming soon",
      description: "File attachment will be available in the next update",
    });
  };

  // Handle key press for sending message
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle typing indicators
  const handleTyping = (value: string) => {
    setNewMessage(value);

    if (!selectedContactId) return;

    if (value.trim() && !isTyping) {
      setIsTyping(true);
      socketService.startTyping(selectedContactId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        socketService.stopTyping(selectedContactId);
      }
    }, 1000);
  };



  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row gap-4 h-[calc(100vh-180px)]">
          {/* Contacts sidebar */}
          <Card className="md:w-1/3 flex flex-col">
            <CardHeader className="pb-2">
              <CardTitle>Messages</CardTitle>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search contacts..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </CardHeader>
            <CardContent className="flex-grow overflow-auto">
              {isLoading ? (
                <div className="text-center text-muted-foreground py-8">
                  Loading conversations...
                </div>
              ) : filteredConversations.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  No conversations found
                </div>
              ) : (
                <ul className="space-y-2">
                  {filteredConversations.map((conversation) => {
                    const contact = conversation.contact;
                    const lastMessage = conversation.latestMessage;
                    const unreadCount = conversation.unreadCount;

                    return (
                      <li key={conversation.contactId}>
                        <button
                          className={`w-full text-left p-3 rounded-md flex items-start gap-3 ${
                            selectedContactId === conversation.contactId
                              ? "bg-primary text-primary-foreground"
                              : "hover:bg-muted"
                          }`}
                          onClick={() => setSelectedContactId(conversation.contactId)}
                        >
                          <div className="flex-shrink-0">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={contact.image} />
                              <AvatarFallback>
                                {getInitials(contact.name)}
                              </AvatarFallback>
                            </Avatar>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between">
                              <p className="font-medium truncate">{contact.name}</p>
                              {lastMessage && (
                                <p className={`text-xs ${
                                  selectedContactId === conversation.contactId
                                    ? "text-primary-foreground/70"
                                    : "text-muted-foreground"
                                }`}>
                                  {getTimeSince(lastMessage.timestamp)}
                                </p>
                              )}
                            </div>
                            <p className={`text-sm truncate mt-1 ${
                              selectedContactId === conversation.contactId
                                ? "text-primary-foreground/70"
                                : "text-muted-foreground"
                            }`}>
                              {lastMessage?.content || "No messages yet"}
                            </p>
                            <div className="flex justify-between items-center mt-1">
                              <Badge variant={selectedContactId === conversation.contactId ? "outline" : "secondary"} className="text-xs">
                                {contact.role === "doctor" ? "Doctor" : "Patient"}
                              </Badge>
                              {unreadCount > 0 && (
                                <Badge variant="destructive" className="ml-auto">
                                  {unreadCount}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </button>
                      </li>
                    );
                  })}
                </ul>
              )}
            </CardContent>
          </Card>
          
          {/* Message area */}
          <Card className="md:w-2/3 flex flex-col">
            {selectedContact ? (
              <>
                <CardHeader className="pb-3 border-b">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={selectedContact.image} />
                      <AvatarFallback>
                        {getInitials(selectedContact.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-base">{selectedContact.name}</CardTitle>
                      <p className="text-xs text-muted-foreground">
                        {selectedContact.role === "doctor" 
                          ? (selectedContact as Doctor).specialty 
                          : "Patient"}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="flex-grow overflow-auto p-4">
                  {messages.length === 0 ? (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <MessageIcon className="h-12 w-12 mx-auto text-muted-foreground" />
                        <h3 className="mt-4 text-lg font-medium">No messages yet</h3>
                        <p className="mt-2 text-sm text-muted-foreground max-w-xs">
                          Start a conversation with {selectedContact.name} by sending a message below.
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((msg, index) => {
                        const isCurrentUser = msg.senderId._id === user?.id;
                        const senderContact = isCurrentUser ? user : selectedContact;
                        
                        return (
                          <div
                            key={msg._id}
                            className={`flex ${isCurrentUser ? "justify-end" : "justify-start"}`}
                          >
                            <div className="flex items-start gap-2 max-w-[75%]">
                              {!isCurrentUser && (
                                <Avatar className="h-8 w-8 mt-0.5">
                                  <AvatarImage src={selectedContact.image} />
                                  <AvatarFallback>
                                    {getInitials(selectedContact.name)}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                              <div>
                                <div
                                  className={`p-3 rounded-lg ${
                                    isCurrentUser
                                      ? "bg-primary text-primary-foreground"
                                      : "bg-muted"
                                  }`}
                                >
                                  {msg.messageType === "text" ? (
                                    <p>{msg.content}</p>
                                  ) : msg.messageType === "image" ? (
                                    <div>
                                      <ImageIcon className="h-4 w-4 mb-1" />
                                      <p>Image attachment</p>
                                    </div>
                                  ) : (
                                    <div>
                                      <FileIcon className="h-4 w-4 mb-1" />
                                      <p>Document attachment</p>
                                    </div>
                                  )}
                                </div>
                                <p className={`text-xs mt-1 ${
                                  isCurrentUser ? "text-right" : ""
                                } text-muted-foreground`}>
                                  {formatMessageTime(msg.timestamp)}
                                  {isCurrentUser && (
                                    <span className="ml-1">
                                      {msg.isRead ? "✓✓" : "✓"}
                                    </span>
                                  )}
                                </p>
                              </div>
                              {isCurrentUser && (
                                <Avatar className="h-8 w-8 mt-0.5">
                                  <AvatarImage src={user?.image} />
                                  <AvatarFallback>
                                    {user ? getInitials(user.name) : "U"}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                            </div>
                          </div>
                        );
                      })}

                      {/* Typing indicator */}
                      {typingUsers.length > 0 && (
                        <div className="flex justify-start">
                          <div className="flex items-start gap-2 max-w-[75%]">
                            <Avatar className="h-8 w-8 mt-0.5">
                              <AvatarImage src={selectedContact?.image} />
                              <AvatarFallback>
                                {selectedContact ? getInitials(selectedContact.name) : "U"}
                              </AvatarFallback>
                            </Avatar>
                            <div className="bg-muted p-3 rounded-lg">
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </CardContent>
                <div className="p-4 border-t">
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="icon" 
                      onClick={handleAttachment}
                    >
                      <PaperclipIcon className="h-4 w-4" />
                    </Button>
                    <Textarea
                      placeholder={`Message ${selectedContact?.name}...`}
                      value={newMessage}
                      onChange={(e) => handleTyping(e.target.value)}
                      onKeyDown={handleKeyPress}
                      className="min-h-[60px] resize-none"
                    />
                    <Button 
                      size="icon" 
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <UserRound className="h-12 w-12 mx-auto text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">No Contact Selected</h3>
                  <p className="mt-2 text-sm text-muted-foreground max-w-xs">
                    Select a contact from the list to start messaging.
                  </p>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Messages;
