const Appointment = require('../models/doctor/Appointment');
const User = require('../models/User');

/**
 * Get valid contacts for a user based on their appointment relationships
 * @param {string} userId - The user's ID
 * @param {string} userRole - The user's role ('doctor' or 'patient')
 * @returns {Array} Array of contact objects with user details
 */
const getValidContacts = async (userId, userRole) => {
  try {
    let appointments;
    let contacts = [];

    if (userRole === 'doctor') {
      // For doctors, get all their patients from appointments
      appointments = await Appointment.find({ doctorId: userId })
        .populate('patientId', 'name email role')
        .select('patientId');

      // Extract unique patient contacts
      const patientIds = new Set();
      appointments.forEach(appointment => {
        if (appointment.patientId && !patientIds.has(appointment.patientId._id.toString())) {
          patientIds.add(appointment.patientId._id.toString());
          contacts.push({
            id: appointment.patientId._id.toString(),
            name: appointment.patientId.name,
            email: appointment.patientId.email,
            role: appointment.patientId.role,
            image: null // You can add image field to User model if needed
          });
        }
      });

    } else if (userRole === 'patient') {
      // For patients, get all their doctors from appointments
      appointments = await Appointment.find({ patientId: userId })
        .populate('doctorId', 'name email role')
        .select('doctorId');

      // Extract unique doctor contacts
      const doctorIds = new Set();
      appointments.forEach(appointment => {
        if (appointment.doctorId && !doctorIds.has(appointment.doctorId._id.toString())) {
          doctorIds.add(appointment.doctorId._id.toString());
          contacts.push({
            id: appointment.doctorId._id.toString(),
            name: appointment.doctorId.name,
            email: appointment.doctorId.email,
            role: appointment.doctorId.role,
            image: null // You can add image field to User model if needed
          });
        }
      });
    }

    return contacts;
  } catch (error) {
    console.error('Error getting valid contacts:', error);
    return [];
  }
};

/**
 * Check if two users have a valid appointment relationship
 * @param {string} userId1 - First user's ID
 * @param {string} userId2 - Second user's ID
 * @param {string} userRole1 - First user's role
 * @returns {boolean} True if they have an appointment relationship
 */
const hasAppointmentRelationship = async (userId1, userId2, userRole1) => {
  try {
    let appointment;

    if (userRole1 === 'doctor') {
      appointment = await Appointment.findOne({
        doctorId: userId1,
        patientId: userId2
      });
    } else if (userRole1 === 'patient') {
      appointment = await Appointment.findOne({
        patientId: userId1,
        doctorId: userId2
      });
    }

    return !!appointment;
  } catch (error) {
    console.error('Error checking appointment relationship:', error);
    return false;
  }
};

/**
 * Get the most recent appointment between two users
 * @param {string} userId1 - First user's ID
 * @param {string} userId2 - Second user's ID
 * @param {string} userRole1 - First user's role
 * @returns {Object|null} The appointment object or null
 */
const getRecentAppointment = async (userId1, userId2, userRole1) => {
  try {
    let appointment;

    if (userRole1 === 'doctor') {
      appointment = await Appointment.findOne({
        doctorId: userId1,
        patientId: userId2
      }).sort({ createdAt: -1 });
    } else if (userRole1 === 'patient') {
      appointment = await Appointment.findOne({
        patientId: userId1,
        doctorId: userId2
      }).sort({ createdAt: -1 });
    }

    return appointment;
  } catch (error) {
    console.error('Error getting recent appointment:', error);
    return null;
  }
};

/**
 * Get contact details by user ID
 * @param {string} userId - The user's ID
 * @returns {Object|null} User details or null
 */
const getContactDetails = async (userId) => {
  try {
    const user = await User.findById(userId).select('name email role');
    if (!user) return null;

    return {
      id: user._id.toString(),
      name: user.name,
      email: user.email,
      role: user.role,
      image: null // Add image field if needed
    };
  } catch (error) {
    console.error('Error getting contact details:', error);
    return null;
  }
};

module.exports = {
  getValidContacts,
  hasAppointmentRelationship,
  getRecentAppointment,
  getContactDetails
};
