const PatientRecord = require('../models/doctor/PatientRecord');
const User = require('../models/User');

/**
 * Get valid contacts for a user based on their patient record relationships
 * @param {string} userId - The user's ID
 * @param {string} userRole - The user's role ('doctor' or 'patient')
 * @returns {Array} Array of contact objects with user details
 */
const getValidContacts = async (userId, userRole) => {
  try {
    let patientRecords;
    let contacts = [];

    if (userRole === 'doctor') {
      // For doctors, get all their patients from patient records
      patientRecords = await PatientRecord.find({ doctor: userId })
        .select('name email');

      // For each patient record, find the corresponding User account
      const patientIds = new Set();
      for (const record of patientRecords) {
        const patientUser = await User.findOne({ email: record.email, role: 'patient' });
        if (patientUser && !patientIds.has(patientUser._id.toString())) {
          patientIds.add(patientUser._id.toString());
          contacts.push({
            id: patientUser._id.toString(),
            name: patientUser.name,
            email: patientUser.email,
            role: patientUser.role,
            image: null // You can add image field to User model if needed
          });
        }
      }

    } else if (userRole === 'patient') {
      // For patients, get all their doctors from patient records
      const currentUser = await User.findById(userId);
      if (currentUser) {
        patientRecords = await PatientRecord.find({ email: currentUser.email })
          .populate('doctor', 'name email role')
          .select('doctor');

        // Extract unique doctor contacts
        const doctorIds = new Set();
        patientRecords.forEach(record => {
          if (record.doctor && !doctorIds.has(record.doctor._id.toString())) {
            doctorIds.add(record.doctor._id.toString());
            contacts.push({
              id: record.doctor._id.toString(),
              name: record.doctor.name,
              email: record.doctor.email,
              role: record.doctor.role,
              image: null // You can add image field to User model if needed
            });
          }
        });
      }
    }

    return contacts;
  } catch (error) {
    console.error('Error getting valid contacts:', error);
    return [];
  }
};

/**
 * Check if two users have a valid patient record relationship
 * @param {string} userId1 - First user's ID
 * @param {string} userId2 - Second user's ID
 * @param {string} userRole1 - First user's role
 * @returns {boolean} True if they have a patient record relationship
 */
const hasPatientRecordRelationship = async (userId1, userId2, userRole1) => {
  try {
    if (userRole1 === 'doctor') {
      // For doctors, check if there's a patient record where they are the doctor
      const user2 = await User.findById(userId2);
      if (!user2) return false;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId1,
        email: user2.email
      });
      return !!patientRecord;
    } else if (userRole1 === 'patient') {
      // For patients, check if there's a patient record where user2 is the doctor
      const user1 = await User.findById(userId1);
      if (!user1) return false;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId2,
        email: user1.email
      });
      return !!patientRecord;
    }

    return false;
  } catch (error) {
    console.error('Error checking patient record relationship:', error);
    return false;
  }
};

/**
 * Get the most recent patient record between two users
 * @param {string} userId1 - First user's ID
 * @param {string} userId2 - Second user's ID
 * @param {string} userRole1 - First user's role
 * @returns {Object|null} The patient record object or null
 */
const getRecentPatientRecord = async (userId1, userId2, userRole1) => {
  try {
    if (userRole1 === 'doctor') {
      // For doctors, find patient record where they are the doctor
      const user2 = await User.findById(userId2);
      if (!user2) return null;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId1,
        email: user2.email
      }).sort({ createdAt: -1 });
      return patientRecord;
    } else if (userRole1 === 'patient') {
      // For patients, find patient record where user2 is the doctor
      const user1 = await User.findById(userId1);
      if (!user1) return null;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId2,
        email: user1.email
      }).sort({ createdAt: -1 });
      return patientRecord;
    }

    return null;
  } catch (error) {
    console.error('Error getting recent patient record:', error);
    return null;
  }
};

/**
 * Get contact details by user ID
 * @param {string} userId - The user's ID
 * @returns {Object|null} User details or null
 */
const getContactDetails = async (userId) => {
  try {
    const user = await User.findById(userId).select('name email role');
    if (!user) return null;

    return {
      id: user._id.toString(),
      name: user.name,
      email: user.email,
      role: user.role,
      image: null // Add image field if needed
    };
  } catch (error) {
    console.error('Error getting contact details:', error);
    return null;
  }
};

module.exports = {
  getValidContacts,
  hasPatientRecordRelationship,
  getRecentPatientRecord,
  getContactDetails
};
