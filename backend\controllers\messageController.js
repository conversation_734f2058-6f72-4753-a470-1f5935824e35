const Message = require('../models/doctor/Message');
const PatientRecord = require('../models/doctor/PatientRecord');
const User = require('../models/User');

// Get all conversations for the current user
const getConversations = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get all patient records for this user (either as doctor or patient)
    let patientRecords;
    let contactIds = new Set();

    if (userRole === 'doctor') {
      // For doctors, find all patient records where they are the doctor
      patientRecords = await PatientRecord.find({ doctor: userId })
        .select('_id name email');

      // For doctors, contacts are their patients (we need to find User accounts with matching emails)
      for (const record of patientRecords) {
        const patientUser = await User.findOne({ email: record.email, role: 'patient' });
        if (patientUser) {
          contactIds.add(patientUser._id.toString());
        }
      }
    } else if (userRole === 'patient') {
      // For patients, find patient records where their email matches
      const currentUser = await User.findById(userId);
      patientRecords = await PatientRecord.find({ email: currentUser.email })
        .populate('doctor', 'name email role')
        .select('doctor');

      // For patients, contacts are their doctors
      patientRecords.forEach(record => {
        if (record.doctor) {
          contactIds.add(record.doctor._id.toString());
        }
      });
    } else {
      return res.status(403).json({ message: 'Unauthorized role' });
    }

    // Get the latest message for each conversation
    const conversations = [];
    for (const contactId of contactIds) {
      const conversationId = Message.generateConversationId(userId, contactId);

      const latestMessage = await Message.findOne({ conversationId })
        .sort({ timestamp: -1 })
        .populate('senderId', 'name')
        .populate('receiverId', 'name');

      const contact = await User.findById(contactId).select('name email role');

      const unreadCount = await Message.countDocuments({
        conversationId,
        receiverId: userId,
        isRead: false
      });

      conversations.push({
        contactId,
        contact,
        latestMessage,
        unreadCount,
        conversationId
      });
    }

    // Sort by latest message timestamp
    conversations.sort((a, b) => {
      const aTime = a.latestMessage ? new Date(a.latestMessage.timestamp) : new Date(0);
      const bTime = b.latestMessage ? new Date(b.latestMessage.timestamp) : new Date(0);
      return bTime - aTime;
    });

    res.status(200).json(conversations);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({ message: 'Server error fetching conversations' });
  }
};

// Get messages for a specific conversation
const getMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.params;

    // Verify that the user has a patient record relationship with this contact
    const hasRelationship = await verifyPatientRecordRelationship(userId, contactId, req.user.role);
    if (!hasRelationship) {
      return res.status(403).json({ message: 'No patient record relationship found' });
    }

    const conversationId = Message.generateConversationId(userId, contactId);

    const messages = await Message.find({ conversationId })
      .sort({ timestamp: 1 })
      .populate('senderId', 'name')
      .populate('receiverId', 'name');

    // Mark messages as read
    await Message.updateMany(
      { conversationId, receiverId: userId, isRead: false },
      { isRead: true }
    );

    res.status(200).json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ message: 'Server error fetching messages' });
  }
};

// Send a new message
const sendMessage = async (req, res) => {
  try {
    const userId = req.user.id;
    const { receiverId, content, messageType = 'text' } = req.body;

    // Verify that the user has a patient record relationship with the receiver
    const hasRelationship = await verifyPatientRecordRelationship(userId, receiverId, req.user.role);
    if (!hasRelationship) {
      return res.status(403).json({ message: 'No patient record relationship found' });
    }

    // Get patient record ID for reference
    const patientRecord = await getPatientRecordBetweenUsers(userId, receiverId, req.user.role);
    if (!patientRecord) {
      return res.status(403).json({ message: 'No valid patient record found' });
    }

    // Get sender and receiver details
    const sender = await User.findById(userId).select('name');
    const receiver = await User.findById(receiverId).select('name');

    const conversationId = Message.generateConversationId(userId, receiverId);

    const newMessage = new Message({
      senderId: userId,
      receiverId,
      senderName: sender.name,
      receiverName: receiver.name,
      content,
      messageType,
      conversationId,
      patientRecordId: patientRecord._id,
      timestamp: new Date()
    });

    const savedMessage = await newMessage.save();

    // Populate the saved message for response
    const populatedMessage = await Message.findById(savedMessage._id)
      .populate('senderId', 'name')
      .populate('receiverId', 'name');

    res.status(201).json(populatedMessage);
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ message: 'Server error sending message' });
  }
};

// Helper function to verify patient record relationship
const verifyPatientRecordRelationship = async (userId, contactId, userRole) => {
  try {
    if (userRole === 'doctor') {
      // For doctors, check if there's a patient record where they are the doctor
      // and the contact is a patient with matching email
      const contactUser = await User.findById(contactId);
      if (!contactUser) return false;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId,
        email: contactUser.email
      });
      return !!patientRecord;
    } else if (userRole === 'patient') {
      // For patients, check if there's a patient record where the contact is the doctor
      // and the patient email matches current user
      const currentUser = await User.findById(userId);
      if (!currentUser) return false;

      const patientRecord = await PatientRecord.findOne({
        doctor: contactId,
        email: currentUser.email
      });
      return !!patientRecord;
    }

    return false;
  } catch (error) {
    console.error('Error verifying patient record relationship:', error);
    return false;
  }
};

// Helper function to get patient record between users
const getPatientRecordBetweenUsers = async (userId, contactId, userRole) => {
  try {
    if (userRole === 'doctor') {
      // For doctors, find patient record where they are the doctor
      const contactUser = await User.findById(contactId);
      if (!contactUser) return null;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId,
        email: contactUser.email
      }).sort({ createdAt: -1 }); // Get the most recent record
      return patientRecord;
    } else if (userRole === 'patient') {
      // For patients, find patient record where the contact is the doctor
      const currentUser = await User.findById(userId);
      if (!currentUser) return null;

      const patientRecord = await PatientRecord.findOne({
        doctor: contactId,
        email: currentUser.email
      }).sort({ createdAt: -1 }); // Get the most recent record
      return patientRecord;
    }

    return null;
  } catch (error) {
    console.error('Error getting patient record between users:', error);
    return null;
  }
};

// Mark messages as read
const markAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.params;

    const conversationId = Message.generateConversationId(userId, contactId);
    
    await Message.updateMany(
      { conversationId, receiverId: userId, isRead: false },
      { isRead: true }
    );

    res.status(200).json({ message: 'Messages marked as read' });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({ message: 'Server error marking messages as read' });
  }
};

module.exports = {
  getConversations,
  getMessages,
  sendMessage,
  markAsRead
};
