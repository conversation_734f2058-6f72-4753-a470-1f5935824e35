const Message = require('../models/doctor/Message');
const Appointment = require('../models/doctor/Appointment');
const User = require('../models/User');

// Get all conversations for the current user
const getConversations = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get all appointments for this user (either as doctor or patient)
    let appointments;
    if (userRole === 'doctor') {
      appointments = await Appointment.find({ doctorId: userId })
        .populate('patientId', 'name email')
        .select('patientId');
    } else if (userRole === 'patient') {
      appointments = await Appointment.find({ patientId: userId })
        .populate('doctorId', 'name email')
        .select('doctorId');
    } else {
      return res.status(403).json({ message: 'Unauthorized role' });
    }

    // Extract unique contact IDs
    const contactIds = new Set();
    appointments.forEach(appointment => {
      if (userRole === 'doctor' && appointment.patientId) {
        contactIds.add(appointment.patientId._id.toString());
      } else if (userRole === 'patient' && appointment.doctorId) {
        contactIds.add(appointment.doctorId._id.toString());
      }
    });

    // Get the latest message for each conversation
    const conversations = [];
    for (const contactId of contactIds) {
      const conversationId = Message.generateConversationId(userId, contactId);
      
      const latestMessage = await Message.findOne({ conversationId })
        .sort({ timestamp: -1 })
        .populate('senderId', 'name')
        .populate('receiverId', 'name');

      const contact = await User.findById(contactId).select('name email role');
      
      const unreadCount = await Message.countDocuments({
        conversationId,
        receiverId: userId,
        isRead: false
      });

      conversations.push({
        contactId,
        contact,
        latestMessage,
        unreadCount,
        conversationId
      });
    }

    // Sort by latest message timestamp
    conversations.sort((a, b) => {
      const aTime = a.latestMessage ? new Date(a.latestMessage.timestamp) : new Date(0);
      const bTime = b.latestMessage ? new Date(b.latestMessage.timestamp) : new Date(0);
      return bTime - aTime;
    });

    res.status(200).json(conversations);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({ message: 'Server error fetching conversations' });
  }
};

// Get messages for a specific conversation
const getMessages = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.params;

    // Verify that the user has an appointment with this contact
    const hasAppointment = await verifyAppointmentRelationship(userId, contactId, req.user.role);
    if (!hasAppointment) {
      return res.status(403).json({ message: 'No appointment relationship found' });
    }

    const conversationId = Message.generateConversationId(userId, contactId);
    
    const messages = await Message.find({ conversationId })
      .sort({ timestamp: 1 })
      .populate('senderId', 'name')
      .populate('receiverId', 'name');

    // Mark messages as read
    await Message.updateMany(
      { conversationId, receiverId: userId, isRead: false },
      { isRead: true }
    );

    res.status(200).json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ message: 'Server error fetching messages' });
  }
};

// Send a new message
const sendMessage = async (req, res) => {
  try {
    const userId = req.user.id;
    const { receiverId, content, messageType = 'text' } = req.body;

    // Verify that the user has an appointment with the receiver
    const hasAppointment = await verifyAppointmentRelationship(userId, receiverId, req.user.role);
    if (!hasAppointment) {
      return res.status(403).json({ message: 'No appointment relationship found' });
    }

    // Get appointment ID for reference
    const appointment = await getAppointmentBetweenUsers(userId, receiverId, req.user.role);
    if (!appointment) {
      return res.status(403).json({ message: 'No valid appointment found' });
    }

    // Get sender and receiver details
    const sender = await User.findById(userId).select('name');
    const receiver = await User.findById(receiverId).select('name');

    const conversationId = Message.generateConversationId(userId, receiverId);

    const newMessage = new Message({
      senderId: userId,
      receiverId,
      senderName: sender.name,
      receiverName: receiver.name,
      content,
      messageType,
      conversationId,
      appointmentId: appointment._id,
      timestamp: new Date()
    });

    const savedMessage = await newMessage.save();
    
    // Populate the saved message for response
    const populatedMessage = await Message.findById(savedMessage._id)
      .populate('senderId', 'name')
      .populate('receiverId', 'name');

    res.status(201).json(populatedMessage);
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ message: 'Server error sending message' });
  }
};

// Helper function to verify appointment relationship
const verifyAppointmentRelationship = async (userId, contactId, userRole) => {
  try {
    let appointment;
    
    if (userRole === 'doctor') {
      appointment = await Appointment.findOne({
        doctorId: userId,
        patientId: contactId
      });
    } else if (userRole === 'patient') {
      appointment = await Appointment.findOne({
        patientId: userId,
        doctorId: contactId
      });
    }

    return !!appointment;
  } catch (error) {
    console.error('Error verifying appointment relationship:', error);
    return false;
  }
};

// Helper function to get appointment between users
const getAppointmentBetweenUsers = async (userId, contactId, userRole) => {
  try {
    let appointment;
    
    if (userRole === 'doctor') {
      appointment = await Appointment.findOne({
        doctorId: userId,
        patientId: contactId
      }).sort({ createdAt: -1 }); // Get the most recent appointment
    } else if (userRole === 'patient') {
      appointment = await Appointment.findOne({
        patientId: userId,
        doctorId: contactId
      }).sort({ createdAt: -1 }); // Get the most recent appointment
    }

    return appointment;
  } catch (error) {
    console.error('Error getting appointment between users:', error);
    return null;
  }
};

// Mark messages as read
const markAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.params;

    const conversationId = Message.generateConversationId(userId, contactId);
    
    await Message.updateMany(
      { conversationId, receiverId: userId, isRead: false },
      { isRead: true }
    );

    res.status(200).json({ message: 'Messages marked as read' });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({ message: 'Server error marking messages as read' });
  }
};

module.exports = {
  getConversations,
  getMessages,
  sendMessage,
  markAsRead
};
