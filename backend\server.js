const express = require('express');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const connectDB = require('./db/connect');
const userRoutes = require('./routes/userRoutes');
const alertRoutes = require('./routes/alertRoutes');
const messageRoutes = require('./routes/messageRoutes');
const doctorDashboardRoutes = require('./routes/doctor/dashboardRoutes');
const doctorProfileRoutes = require('./routes/doctor/profileRoutes');
const patientRoutes = require('./routes/doctor/patientRoutes');
const doctorReportRoutes = require('./routes/doctor/reportRoutes');
const User = require('./models/User');
const Message = require('./models/doctor/Message');
const path = require('path');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:8080",
    methods: ["GET", "POST"]
  }
});
const port = process.env.PORT || 5000;

if (!process.env.MONGO_URI) {
  console.error('Error: MONGO_URI is not defined in .env file');
  process.exit(1);
}

app.use(cors());
app.use(express.json());
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Helper functions for Socket.IO
const PatientRecord = require('./models/doctor/PatientRecord');

const verifyPatientRecordRelationship = async (userId, contactId, userRole) => {
  try {
    if (userRole === 'doctor') {
      // For doctors, check if there's a patient record where they are the doctor
      const contactUser = await User.findById(contactId);
      if (!contactUser) return false;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId,
        email: contactUser.email
      });
      return !!patientRecord;
    } else if (userRole === 'patient') {
      // For patients, check if there's a patient record where the contact is the doctor
      const currentUser = await User.findById(userId);
      if (!currentUser) return false;

      const patientRecord = await PatientRecord.findOne({
        doctor: contactId,
        email: currentUser.email
      });
      return !!patientRecord;
    }

    return false;
  } catch (error) {
    console.error('Error verifying patient record relationship:', error);
    return false;
  }
};

const getPatientRecordBetweenUsers = async (userId, contactId, userRole) => {
  try {
    if (userRole === 'doctor') {
      // For doctors, find patient record where they are the doctor
      const contactUser = await User.findById(contactId);
      if (!contactUser) return null;

      const patientRecord = await PatientRecord.findOne({
        doctor: userId,
        email: contactUser.email
      }).sort({ createdAt: -1 });
      return patientRecord;
    } else if (userRole === 'patient') {
      // For patients, find patient record where the contact is the doctor
      const currentUser = await User.findById(userId);
      if (!currentUser) return null;

      const patientRecord = await PatientRecord.findOne({
        doctor: contactId,
        email: currentUser.email
      }).sort({ createdAt: -1 });
      return patientRecord;
    }

    return null;
  } catch (error) {
    console.error('Error getting patient record between users:', error);
    return null;
  }
};

const startServer = async () => {
  try {
    await connectDB();
    console.log('MongoDB connected successfully');

    app.use('/api', userRoutes);
    app.use('/api/alerts', alertRoutes);
    app.use('/api/messages', messageRoutes);

    app.use('/api/doctor', (req, res, next) => {
      console.log(`>>> Doctor Path Request: ${req.method} ${req.originalUrl}`);
      next();
    });

    app.use('/api/doctor/dashboard', doctorDashboardRoutes);
    app.use('/api/doctor/profile', doctorProfileRoutes);
    app.use('/api/doctor/patient', patientRoutes);
    app.use('/api/doctor/reports', doctorReportRoutes);

    app.use((err, req, res, next) => {
      console.error("Unhandled Error:", err.stack || err);
      const statusCode = err.statusCode || 500;
      const message = process.env.NODE_ENV === 'production' ? 'Internal Server Error' : err.message || 'Something went wrong!';
      if (!res.headersSent) {
        res.status(statusCode).json({ message });
      } else {
        next(err);
      }
    });

    // Socket.IO authentication middleware
    io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('Authentication error'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).select('-password');

        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user._id.toString();
        socket.userRole = user.role;
        socket.userName = user.name;
        next();
      } catch (error) {
        next(new Error('Authentication error'));
      }
    });

    // Socket.IO connection handling
    io.on('connection', (socket) => {
      console.log(`User ${socket.userName} (${socket.userRole}) connected`);

      // Join user to their personal room
      socket.join(socket.userId);

      // Handle joining conversation rooms
      socket.on('join_conversation', (contactId) => {
        const conversationId = Message.generateConversationId(socket.userId, contactId);
        socket.join(conversationId);
        console.log(`User ${socket.userName} joined conversation ${conversationId}`);
      });

      // Handle leaving conversation rooms
      socket.on('leave_conversation', (contactId) => {
        const conversationId = Message.generateConversationId(socket.userId, contactId);
        socket.leave(conversationId);
        console.log(`User ${socket.userName} left conversation ${conversationId}`);
      });

      // Handle new messages
      socket.on('send_message', async (data) => {
        try {
          const { receiverId, content, messageType = 'text' } = data;

          // Verify patient record relationship (same logic as in controller)
          const hasRelationship = await verifyPatientRecordRelationship(socket.userId, receiverId, socket.userRole);
          if (!hasRelationship) {
            socket.emit('error', { message: 'No patient record relationship found' });
            return;
          }

          // Get patient record and user details
          const patientRecord = await getPatientRecordBetweenUsers(socket.userId, receiverId, socket.userRole);
          const receiver = await User.findById(receiverId).select('name');

          if (!patientRecord || !receiver) {
            socket.emit('error', { message: 'Invalid recipient or patient record' });
            return;
          }

          const conversationId = Message.generateConversationId(socket.userId, receiverId);

          const newMessage = new Message({
            senderId: socket.userId,
            receiverId,
            senderName: socket.userName,
            receiverName: receiver.name,
            content,
            messageType,
            conversationId,
            patientRecordId: patientRecord._id,
            timestamp: new Date()
          });

          const savedMessage = await newMessage.save();
          const populatedMessage = await Message.findById(savedMessage._id)
            .populate('senderId', 'name')
            .populate('receiverId', 'name');

          // Emit to conversation room
          io.to(conversationId).emit('new_message', populatedMessage);

          // Emit to receiver's personal room for notifications
          io.to(receiverId).emit('message_notification', {
            senderId: socket.userId,
            senderName: socket.userName,
            conversationId,
            message: populatedMessage
          });

        } catch (error) {
          console.error('Error sending message via socket:', error);
          socket.emit('error', { message: 'Failed to send message' });
        }
      });

      // Handle typing indicators
      socket.on('typing_start', (contactId) => {
        const conversationId = Message.generateConversationId(socket.userId, contactId);
        socket.to(conversationId).emit('user_typing', {
          userId: socket.userId,
          userName: socket.userName
        });
      });

      socket.on('typing_stop', (contactId) => {
        const conversationId = Message.generateConversationId(socket.userId, contactId);
        socket.to(conversationId).emit('user_stopped_typing', {
          userId: socket.userId
        });
      });

      // Handle message read status
      socket.on('mark_messages_read', async (contactId) => {
        try {
          const conversationId = Message.generateConversationId(socket.userId, contactId);

          await Message.updateMany(
            { conversationId, receiverId: socket.userId, isRead: false },
            { isRead: true }
          );

          // Notify sender that messages were read
          socket.to(conversationId).emit('messages_read', {
            readBy: socket.userId,
            conversationId
          });

        } catch (error) {
          console.error('Error marking messages as read:', error);
        }
      });

      socket.on('disconnect', () => {
        console.log(`User ${socket.userName} disconnected`);
      });
    });

    server.listen(port, () => {
      console.log(`Server running at http://localhost:${port}`);
    });
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error.message);
    process.exit(1);
  }
};

startServer();