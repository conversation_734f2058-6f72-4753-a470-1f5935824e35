import axios from 'axios';

const API_BASE_URL = '/api/messages';

// Create axios instance with default config
const messageAPI = axios.create({
  baseURL: API_BASE_URL,
});

// Add auth token to requests
messageAPI.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export interface MessageData {
  _id: string;
  senderId: {
    _id: string;
    name: string;
  };
  receiverId: {
    _id: string;
    name: string;
  };
  content: string;
  messageType: 'text' | 'image' | 'file';
  isRead: boolean;
  timestamp: string;
  conversationId: string;
}

export interface ConversationData {
  contactId: string;
  contact: {
    _id: string;
    name: string;
    email: string;
    role: string;
  };
  latestMessage?: MessageData;
  unreadCount: number;
  conversationId: string;
}

export interface ContactData {
  id: string;
  name: string;
  email: string;
  role: string;
  image?: string;
}

class MessageService {
  // Get all conversations for the current user
  async getConversations(): Promise<ConversationData[]> {
    try {
      const response = await messageAPI.get('/conversations');
      return response.data;
    } catch (error) {
      console.error('Error fetching conversations:', error);
      throw error;
    }
  }

  // Get messages for a specific conversation
  async getMessages(contactId: string): Promise<MessageData[]> {
    try {
      const response = await messageAPI.get(`/conversation/${contactId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  }

  // Send a new message
  async sendMessage(receiverId: string, content: string, messageType: string = 'text'): Promise<MessageData> {
    try {
      const response = await messageAPI.post('/send', {
        receiverId,
        content,
        messageType
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Mark messages as read
  async markAsRead(contactId: string): Promise<void> {
    try {
      await messageAPI.put(`/conversation/${contactId}/read`);
    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw error;
    }
  }
}

// Create a singleton instance
const messageService = new MessageService();
export default messageService;
