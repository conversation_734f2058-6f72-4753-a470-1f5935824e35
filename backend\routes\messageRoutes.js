const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/authMiddleware');
const {
  getConversations,
  getMessages,
  sendMessage,
  markAsRead
} = require('../controllers/messageController');

// Apply authentication middleware to all routes
router.use(protect);

// Get all conversations for the current user
router.get('/conversations', getConversations);

// Get messages for a specific conversation
router.get('/conversation/:contactId', getMessages);

// Send a new message
router.post('/send', sendMessage);

// Mark messages as read
router.put('/conversation/:contactId/read', markAsRead);

module.exports = router;
